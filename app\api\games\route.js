import { NextResponse } from 'next/server';
import { ListObjectsV2Command } from '@aws-sdk/client-s3';
import { s3Client, BUCKET_NAME, CLOUDFRONT_DOMAIN } from '@/lib/config';

export async function GET() {
    try {
        const command = new ListObjectsV2Command({
            Bucket: BUCKET_NAME,
            Delimiter: '/' // Get top-level "folders" (game IDs)
        });

        const response = await s3Client.send(command);
        
        const gameIds = response.CommonPrefixes
            ? response.CommonPrefixes
                .map(prefix => prefix.Prefix.replace('/', ''))
                .filter(id => id)
            : [];

        return NextResponse.json({
            games: gameIds.map(id => ({
                id,
                url: `https://${CLOUDFRONT_DOMAIN}/${id}/index.html`
            }))
        });
    } catch (error) {
        console.error('Error listing games:', error);
        return NextResponse.json({
            error: 'Failed to list games',
            details: error.message
        }, { status: 500 });
    }
}
