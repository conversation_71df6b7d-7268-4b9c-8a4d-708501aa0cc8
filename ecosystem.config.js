// PM2 Ecosystem Configuration for Unity Cloud
// This file defines how PM2 should manage your Next.js application

module.exports = {
  apps: [
    {
      // Application name (used in PM2 commands)
      name: 'unity-cloud',
      
      // Script to run (for Next.js, we use npm start)
      script: 'npm',
      args: 'start',
      
      // Working directory
      cwd: '/home/<USER>/unity-cloud', // Change 'ubuntu' to your EC2 username
      
      // Instance settings
      instances: 1,                    // Number of instances (1 for single server)
      exec_mode: 'fork',              // Execution mode (fork or cluster)
      
      // Auto-restart settings
      autorestart: true,              // Automatically restart if app crashes
      watch: false,                   // Don't watch files in production
      max_memory_restart: '1G',       // Restart if memory usage exceeds 1GB
      
      // Environment variables for production
      env: {
        NODE_ENV: 'production',
        PORT: 3000,
        // Add your other environment variables here
        // AWS_REGION: 'us-east-1',
        // DATABASE_URL: 'your-database-url',
      },
      
      // Logging configuration
      log_file: './logs/combined.log',
      out_file: './logs/out.log',
      error_file: './logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // Advanced settings
      min_uptime: '10s',              // Minimum uptime before considering restart
      max_restarts: 10,               // Maximum restarts within restart_delay window
      restart_delay: 4000,            // Delay between restarts (ms)
      
      // Merge logs from all instances
      merge_logs: true,
      
      // Kill timeout
      kill_timeout: 1600,
    }
  ],

  // Deployment configuration (optional - for advanced users)
  deploy: {
    production: {
      user: 'ubuntu',                 // Change to your EC2 username
      host: ['your-ec2-public-ip'],   // Replace with your EC2 IP
      ref: 'origin/main',
      repo: 'https://github.com/yourusername/unity-cloud.git', // Replace with your repo URL
      path: '/home/<USER>/unity-cloud',
      'post-deploy': 'npm ci && npm run build && pm2 reload ecosystem.config.js --env production && pm2 save'
    }
  }
};
