/* Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Base */
:root {
  --primary: #4CAF50;
  --primary-hover: #45a049;
  --background: #f9f9f9;
  --foreground: #333;
  --error: #d32f2f;
  --error-light: #ffebee;
}

body {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: var(--background);
  color: var(--foreground);
  line-height: 1.6;
}

/* Navigation */
.nav-bar {
  background: #1a1a1a;
  padding: 1rem 0;
  margin-bottom: 2rem;
}

.nav-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-logo {
  color: white;
  font-size: 1.5rem;
  text-decoration: none;
  font-weight: bold;
}

.nav-links {
  display: flex;
  gap: 2rem;
}

.nav-link {
  color: white;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.nav-link:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem 2rem;
  min-height: calc(100vh - 80px);
}

/* Game Grid */
.games-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 2rem;
  padding: 2rem 0;
}

.game-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  text-decoration: none;
  color: var(--foreground);
  transition: transform 0.3s, box-shadow 0.3s;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.game-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.game-title {
  font-weight: 600;
}

.game-play-button {
  background: var(--primary);
  color: white;
  padding: 0.5rem 2rem;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.game-play-button:hover {
  background: var(--primary-hover);
}

/* Forms and Inputs */
.upload-form {
  max-width: 600px;
  margin: 2rem auto;
  padding: 2rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.upload-form h1 {
  margin-bottom: 2rem;
  text-align: center;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-group input[type="file"] {
  width: 100%;
  padding: 1rem;
  border: 2px dashed #ddd;
  border-radius: 4px;
  cursor: pointer;
}

.form-group input[type="file"]:hover {
  border-color: var(--primary);
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s;
}

.btn-primary {
  background: var(--primary);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: var(--primary-hover);
}

.btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Messages */
.error-message {
  color: var(--error);
  background: var(--error-light);
  padding: 0.75rem;
  border-radius: 4px;
  margin: 1rem 0;
  text-align: center;
}

.success-message {
  color: var(--primary);
  background: rgba(76, 175, 80, 0.1);
  padding: 0.75rem;
  border-radius: 4px;
  margin: 1rem 0;
  text-align: center;
}

/* Unity Player */
.unity-container {
  width: 100%;
  height: calc(100vh - 80px);
  position: relative;
  background: black;
  border-radius: 8px;
  overflow: hidden;
}

.unity-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  text-align: center;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
  
  .game-card,
  .upload-form {
    background: #1a1a1a;
    color: var(--foreground);
  }
}
