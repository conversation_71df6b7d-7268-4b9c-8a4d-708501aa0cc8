'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import {
    Box,
    Typography,
    Button,
    LinearProgress,
    Alert,
    Paper,
    Container,
    FormControl,
    InputLabel,
    OutlinedInput,
    Select,
    MenuItem,
    Chip
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';

export default function UploadGame() {
    const [file, setFile] = useState(null);
    const [uploading, setUploading] = useState(false);
    const [error, setError] = useState(null);
    const [bucketInfo, setBucketInfo] = useState(null);
    const [clearing, setClearing] = useState(false);
    const [games, setGames] = useState([]);
    const [selectedGames, setSelectedGames] = useState([]);

    useEffect(() => {
        const fetchData = async () => {
            try {
                const [bucketResponse, gamesResponse] = await Promise.all([
                    fetch('/api/bucket'),
                    fetch('/api/games')
                ]);

                if (!bucketResponse.ok || !gamesResponse.ok) {
                    throw new Error('Failed to fetch data');
                }

                const [bucketData, gamesData] = await Promise.all([
                    bucketResponse.json(),
                    gamesResponse.json()
                ]);

                setBucketInfo(bucketData);
                setGames(gamesData.games || []);
            } catch (err) {
                console.error('Error fetching data:', err);
                setError('Failed to load data: ' + err.message);
            }
        };

        fetchData();
        const interval = setInterval(fetchData, 30000);
        return () => clearInterval(interval);
    }, []);

    const handleFileChange = (e) => {
        const selectedFile = e.target.files[0];
        if (selectedFile && selectedFile.name.toLowerCase().endsWith('.zip')) {
            setFile(selectedFile);
            setError(null);
        } else {
            setFile(null);
            setError('Please select a valid ZIP file');
        }
    };

    const handleDeleteGames = async () => {
        if (selectedGames.length === 0) return;

        if (!window.confirm(`Are you sure you want to delete ${selectedGames.length} selected game(s)?`)) {
            return;
        }

        try {
            setClearing(true);
            const response = await fetch('/api/bucket', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ gameIds: selectedGames })
            });

            if (!response.ok) {
                throw new Error('Failed to delete games');
            }

            const [bucketResponse, gamesResponse] = await Promise.all([
                fetch('/api/bucket'),
                fetch('/api/games')
            ]);

            if (bucketResponse.ok && gamesResponse.ok) {
                const [bucketData, gamesData] = await Promise.all([
                    bucketResponse.json(),
                    gamesResponse.json()
                ]);
                setBucketInfo(bucketData);
                setGames(gamesData.games || []);
                setSelectedGames([]);
            }
        } catch (err) {
            setError('Failed to delete games: ' + err.message);
        } finally {
            setClearing(false);
        }
    };

    const handleUpload = async (e) => {
        e.preventDefault();
        if (!file) {
            setError('Please select a file to upload');
            return;
        }

        if (bucketInfo?.isFull) {
            setError('Bucket is full. Please clear some space before uploading.');
            return;
        }

        try {
            setUploading(true);
            setError(null);

            const formData = new FormData();
            formData.append('file', file);

            const response = await fetch('/api/upload-game', {
                method: 'POST',
                body: formData,
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Upload failed');
            }

            const data = await response.json();
            window.location.href = `/play/${data.id}`;
        } catch (err) {
            setError(err.message);
        } finally {
            setUploading(false);
        }
    };

    const handleGameSelection = (event) => {
        setSelectedGames(event.target.value);
    };

    return (
        <Container maxWidth="md" sx={{ py: 4 }}>
            <Paper elevation={3} sx={{ p: 4, backgroundColor: '#1a1a1a', color: 'white' }}>
                <Typography variant="h4" gutterBottom align="center">
                    Upload Unity WebGL Game
                </Typography>

                {error && <Alert severity="error" sx={{ mb: 3 }}>{error}</Alert>}

                {bucketInfo && (
                    <Box sx={{ mb: 4 }}>
                        <Typography variant="subtitle1" gutterBottom>
                            Storage Usage: {bucketInfo.sizeInMB} MB / {bucketInfo.sizeLimit} MB
                        </Typography>
                        <LinearProgress
                            variant="determinate"
                            value={(bucketInfo.sizeInMB / bucketInfo.sizeLimit) * 100}
                            sx={{
                                height: 10,
                                borderRadius: 5,
                                mb: 1,
                                backgroundColor: 'rgba(255,255,255,0.1)',
                                '& .MuiLinearProgress-bar': {
                                    backgroundColor: bucketInfo.isFull ? 'error.main' : 'primary.main'
                                }
                            }}
                        />
                        {bucketInfo.isFull && (
                            <Alert severity="warning" sx={{ mt: 2 }}>
                                Storage is full. Please delete some games before uploading.
                            </Alert>
                        )}
                    </Box>
                )}

                <Box sx={{ mb: 4 }}>
                    <FormControl fullWidth>
                        <InputLabel id="games-select-label" sx={{ color: 'white' }}>
                            Select Games to Delete
                        </InputLabel>
                        <Select
                            labelId="games-select-label"
                            multiple
                            value={selectedGames}
                            onChange={handleGameSelection}
                            input={
                                <OutlinedInput
                                    label="Select Games to Delete"
                                    sx={{
                                        color: 'white',
                                        '&.Mui-disabled': {
                                            color: 'rgba(255,255,255,0.5)',
                                            WebkitTextFillColor: 'rgba(255,255,255,0.5)',
                                        }
                                    }}
                                />
                            }
                            sx={{
                                color: 'white',
                                '& .MuiSvgIcon-root': {
                                    color: 'white',
                                },
                                '&.Mui-disabled .MuiSvgIcon-root': {
                                    color: 'rgba(255,255,255,0.5)',
                                }
                            }}
                            renderValue={(selected) => (
                                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                                    {selected.map((value) => (
                                        <Chip
                                            key={value}
                                            label={value.substring(0, 8) + '...'}
                                            size="small"
                                            sx={{ color: 'white', backgroundColor: '#333' }}
                                        />
                                    ))}
                                </Box>
                            )}
                        >
                            {games.map((game) => (
                                <MenuItem key={game.id} value={game.id}>
                                    {game.id}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>
                    <Button
                        variant="contained"
                        color="error"
                        startIcon={<DeleteIcon />}
                        onClick={handleDeleteGames}
                        disabled={selectedGames.length === 0 || clearing}
                        sx={{
                            mt: 2,
                            color: 'white',
                            '&.Mui-disabled': {
                                backgroundColor: '#555',
                                color: 'rgba(255,255,255,0.5)',
                            }
                        }}
                    >
                        {clearing ? 'Deleting...' : 'Delete Selected Games'}
                    </Button>
                </Box>

                <Box component="form" onSubmit={handleUpload}>
                    <Button
                        component="label"
                        variant="outlined"
                        startIcon={<CloudUploadIcon />}
                        disabled={uploading || clearing || bucketInfo?.isFull}
                        sx={{
                            mb: 2,
                            width: '100%',
                            color: 'white',
                            borderColor: 'white',
                            '&.Mui-disabled': {
                                color: 'rgba(255,255,255,0.5)',
                                borderColor: 'rgba(255,255,255,0.2)',
                            }
                        }}
                    >
                        Choose Unity WebGL Game (ZIP file)
                        <input
                            type="file"
                            hidden
                            onChange={handleFileChange}
                            accept=".zip"
                        />
                    </Button>

                    {file && (
                        <Typography variant="body2" sx={{ mb: 2 }}>
                            Selected file: {file.name}
                        </Typography>
                    )}

                    <Button
                        type="submit"
                        variant="contained"
                        fullWidth
                        disabled={!file || uploading || clearing || bucketInfo?.isFull}
                        sx={{
                            color: 'white',
                            '&.Mui-disabled': {
                                backgroundColor: '#555',
                                color: 'rgba(255,255,255,0.5)',
                            }
                        }}
                    >
                        {uploading ? 'Uploading...' : 'Upload Game'}
                    </Button>

                    {uploading && (
                        <Box sx={{ mt: 2 }}>
                            <LinearProgress />
                            <Typography variant="body2" sx={{ mt: 1, textAlign: 'center' }}>
                                Uploading game files...
                            </Typography>
                        </Box>
                    )}
                </Box>
            </Paper>
        </Container>
    );
}
