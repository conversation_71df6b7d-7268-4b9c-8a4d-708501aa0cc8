'use client';

import { useState, useEffect } from 'react';

export default function UnityPlayer({ params }) {
    const [error, setError] = useState(null);
    const [gameUrl, setGameUrl] = useState(null);
    const [loading, setLoading] = useState(true);
    const [retryCount, setRetryCount] = useState(0);
    const { gameId } = params;

    useEffect(() => {
        let isMounted = true;
        const maxRetries = 3;
        const retryDelay = 2000;

        const fetchGameUrl = async () => {
            try {
                setLoading(true);
                setError(null); const response = await fetch(`/api/game?bucket=gamers-heaven-2&gameId=${gameId}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                if (!data.url) {
                    throw new Error('Game URL not found in response');
                }

                if (isMounted) {
                    setGameUrl(data.url);
                    setLoading(false);
                }
            } catch (err) {
                console.error('Error fetching game:', err);
                if (isMounted) {
                    if (retryCount < maxRetries) {
                        setTimeout(() => {
                            setRetryCount(prev => prev + 1);
                        }, retryDelay);
                    } else {
                        setError(err.message);
                    }
                    setLoading(false);
                }
            }
        };

        if (gameId) {
            fetchGameUrl();
        }

        return () => {
            isMounted = false;
        };
    }, [gameId, retryCount]); return (
        <div
            className="container"
            style={{
                width: '100vw',
                height: '100vh',
                overflow: 'hidden',
                position: 'relative'
            }}
        >
            <div
                className="unity-container"
                style={{
                    // width: '100%',
                    // height: '100%',
                    // position: 'absolute',
                    // top: 0,
                    // left: 0
                }}
            >
                {loading && (
                    <div className="unity-loading" style={{
                        position: 'absolute',
                        top: '50%',
                        left: '50%',
                        transform: 'translate(-50%, -50%)',
                        textAlign: 'center',
                        color: '#fff'
                    }}>
                        <p>Loading game resources...</p>
                        {retryCount > 0 && (
                            <p>Retry attempt {retryCount}/3</p>
                        )}
                    </div>
                )}

                {error && (
                    <div className="unity-error" style={{
                        position: 'absolute',
                        top: '50%',
                        left: '50%',
                        transform: 'translate(-50%, -50%)',
                        textAlign: 'center',
                        color: '#fff'
                    }}>
                        <p>Error loading game: {error}</p>
                        <button
                            className="btn btn-primary"
                            onClick={() => {
                                setRetryCount(0);
                                setError(null);
                            }}
                        >
                            Retry
                        </button>
                    </div>
                )}                {gameUrl && !loading && !error && (
                    <iframe
                        src={gameUrl}
                        style={{
                            width: '100%',
                            height: '100%',
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            border: 'none',
                            background: '#000'
                        }}
                        title="Unity Game"
                        allow="autoplay; fullscreen; cross-origin-isolated"
                    />
                )}
            </div>
        </div>
    );
}
