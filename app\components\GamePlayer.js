'use client';

import React, { useEffect, useCallback, useRef } from 'react';
import { useGameLauncher } from '../../hooks/useGameLauncher';

export default function GamePlayer({ gameId }) {
  const { 
    loading, 
    error, 
    connected, 
    sessionId, 
    videoRef, 
    launchGame, 
    sendInputEvent, 
    isReady 
  } = useGameLauncher(gameId);
  
  const containerRef = useRef(null);
  const isCapturingInput = useRef(false);
  
  // Handle keyboard input
  const handleKeyDown = useCallback((event) => {
    if (!isReady || !isCapturingInput.current) return;
    
    event.preventDefault();
    sendInputEvent({
      type: 'keyboard',
      data: {
        key: event.key,
        code: event.code,
        keyCode: event.keyCode,
        ctrlKey: event.ctrlKey,
        shiftKey: event.shiftKey,
        altKey: event.altKey,
        metaKey: event.metaKey,
        action: 'keydown'
      },
      timestamp: Date.now()
    });
  }, [isReady, sendInputEvent]);
  
  const handleKeyUp = useCallback((event) => {
    if (!isReady || !isCapturingInput.current) return;
    
    event.preventDefault();
    sendInputEvent({
      type: 'keyboard',
      data: {
        key: event.key,
        code: event.code,
        keyCode: event.keyCode,
        ctrlKey: event.ctrlKey,
        shiftKey: event.shiftKey,
        altKey: event.altKey,
        metaKey: event.metaKey,
        action: 'keyup'
      },
      timestamp: Date.now()
    });
  }, [isReady, sendInputEvent]);
  
  // Handle mouse input
  const handleMouseMove = useCallback((event) => {
    if (!isReady || !isCapturingInput.current) return;
    
    const rect = containerRef.current?.getBoundingClientRect();
    if (!rect) return;
    
    sendInputEvent({
      type: 'mouse',
      data: {
        x: event.clientX - rect.left,
        y: event.clientY - rect.top,
        action: 'move'
      },
      timestamp: Date.now()
    });
  }, [isReady, sendInputEvent]);
  
  const handleMouseDown = useCallback((event) => {
    if (!isReady || !isCapturingInput.current) return;
    
    event.preventDefault();
    const rect = containerRef.current?.getBoundingClientRect();
    if (!rect) return;
    
    sendInputEvent({
      type: 'mouse',
      data: {
        x: event.clientX - rect.left,
        y: event.clientY - rect.top,
        button: event.button,
        action: 'down'
      },
      timestamp: Date.now()
    });
  }, [isReady, sendInputEvent]);
  
  const handleMouseUp = useCallback((event) => {
    if (!isReady || !isCapturingInput.current) return;
    
    event.preventDefault();
    const rect = containerRef.current?.getBoundingClientRect();
    if (!rect) return;
    
    sendInputEvent({
      type: 'mouse',
      data: {
        x: event.clientX - rect.left,
        y: event.clientY - rect.top,
        button: event.button,
        action: 'up'
      },
      timestamp: Date.now()
    });
  }, [isReady, sendInputEvent]);
  
  // Handle gamepad input
  const handleGamepadInput = useCallback(() => {
    if (!isReady || !isCapturingInput.current) return;
    
    const gamepads = navigator.getGamepads();
    for (let i = 0; i < gamepads.length; i++) {
      const gamepad = gamepads[i];
      if (gamepad) {
        sendInputEvent({
          type: 'gamepad',
          data: {
            index: i,
            buttons: gamepad.buttons.map(button => ({
              pressed: button.pressed,
              value: button.value
            })),
            axes: Array.from(gamepad.axes)
          },
          timestamp: Date.now()
        });
      }
    }
  }, [isReady, sendInputEvent]);
  
  // Setup input event listeners
  useEffect(() => {
    if (!isReady) return;
    
    // Keyboard events
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);
    
    // Mouse events
    const container = containerRef.current;
    if (container) {
      container.addEventListener('mousemove', handleMouseMove);
      container.addEventListener('mousedown', handleMouseDown);
      container.addEventListener('mouseup', handleMouseUp);
    }
    
    // Gamepad polling
    const gamepadInterval = setInterval(handleGamepadInput, 16); // ~60fps
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('keyup', handleKeyUp);
      
      if (container) {
        container.removeEventListener('mousemove', handleMouseMove);
        container.removeEventListener('mousedown', handleMouseDown);
        container.removeEventListener('mouseup', handleMouseUp);
      }
      
      clearInterval(gamepadInterval);
    };
  }, [isReady, handleKeyDown, handleKeyUp, handleMouseMove, handleMouseDown, handleMouseUp, handleGamepadInput]);
  
  // Auto-launch when gameId is provided
  useEffect(() => {
    if (gameId && !loading && !connected && !error) {
      launchGame();
    }
  }, [gameId, loading, connected, error, launchGame]);
  
  // Focus management for input capture
  const handleContainerClick = useCallback(() => {
    isCapturingInput.current = true;
    containerRef.current?.focus();
  }, []);
  
  const handleContainerBlur = useCallback(() => {
    isCapturingInput.current = false;
  }, []);
  
  return (
    <div 
      ref={containerRef}
      className="game-player-container"
      style={{
        width: '100%',
        height: '100vh',
        position: 'relative',
        background: '#000',
        cursor: isCapturingInput.current ? 'none' : 'pointer',
        outline: 'none'
      }}
      tabIndex={0}
      onClick={handleContainerClick}
      onBlur={handleContainerBlur}
    >
      {/* Loading State */}
      {loading && (
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          textAlign: 'center',
          color: '#fff',
          zIndex: 10
        }}>
          <div style={{ marginBottom: '16px' }}>
            <div style={{ 
              width: '40px', 
              height: '40px', 
              border: '4px solid #333',
              borderTop: '4px solid #fff',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite',
              margin: '0 auto 16px'
            }} />
          </div>
          <p>Connecting to game server...</p>
          {sessionId && <p style={{ fontSize: '12px', opacity: 0.7 }}>Session: {sessionId}</p>}
        </div>
      )}
      
      {/* Error State */}
      {error && (
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          textAlign: 'center',
          color: '#fff',
          zIndex: 10
        }}>
          <p style={{ color: '#ff6b6b', marginBottom: '16px' }}>
            Error: {error}
          </p>
          <button
            onClick={launchGame}
            style={{
              padding: '8px 16px',
              background: '#007bff',
              color: '#fff',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Retry Connection
          </button>
        </div>
      )}
      
      {/* Video Stream */}
      <video
        ref={videoRef}
        autoPlay
        playsInline
        muted={false}
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'contain',
          display: connected ? 'block' : 'none'
        }}
      />
      
      {/* Input Capture Indicator */}
      {isCapturingInput.current && connected && (
        <div style={{
          position: 'absolute',
          top: '10px',
          right: '10px',
          background: 'rgba(0, 123, 255, 0.8)',
          color: '#fff',
          padding: '4px 8px',
          borderRadius: '4px',
          fontSize: '12px',
          zIndex: 20
        }}>
          Input Captured
        </div>
      )}
      
      {/* Instructions */}
      {connected && !isCapturingInput.current && (
        <div style={{
          position: 'absolute',
          bottom: '20px',
          left: '50%',
          transform: 'translateX(-50%)',
          background: 'rgba(0, 0, 0, 0.8)',
          color: '#fff',
          padding: '8px 16px',
          borderRadius: '4px',
          fontSize: '14px',
          zIndex: 20
        }}>
          Click to capture input and start playing
        </div>
      )}
      
      {/* CSS for loading spinner */}
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
}
