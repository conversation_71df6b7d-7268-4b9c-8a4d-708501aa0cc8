import { useState, useEffect, useRef, useCallback } from 'react';

interface PlayResponse {
  mode: 'stream';
  sessionId: string;
  signaling: string;
  iceServers: Array<{ urls: string }>;
}

interface GameLauncherState {
  loading: boolean;
  error: string | null;
  connected: boolean;
  sessionId: string | null;
  videoRef: React.RefObject<HTMLVideoElement>;
}

interface InputEvent {
  type: 'keyboard' | 'mouse' | 'gamepad';
  data: any;
  timestamp: number;
}

export function useGameLauncher(gameId: string) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [connected, setConnected] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  
  const peerConnectionRef = useRef<RTCPeerConnection | null>(null);
  const dataChannelRef = useRef<RTCDataChannel | null>(null);
  const signalingSocketRef = useRef<WebSocket | null>(null);
  
  // Send input event via WebRTC data channel
  const sendInputEvent = useCallback((event: InputEvent) => {
    if (dataChannelRef.current && dataChannelRef.current.readyState === 'open') {
      try {
        dataChannelRef.current.send(JSON.stringify(event));
      } catch (error) {
        console.error('Failed to send input event:', error);
      }
    }
  }, []);
  
  // Setup WebRTC connection
  const setupWebRTC = useCallback(async (playResponse: PlayResponse) => {
    try {
      // Create peer connection
      const peerConnection = new RTCPeerConnection({
        iceServers: playResponse.iceServers
      });
      
      peerConnectionRef.current = peerConnection;
      
      // Handle incoming video stream
      peerConnection.ontrack = (event) => {
        console.log('Received remote stream');
        if (videoRef.current && event.streams[0]) {
          videoRef.current.srcObject = event.streams[0];
        }
      };
      
      // Create data channel for input
      const dataChannel = peerConnection.createDataChannel('input', {
        ordered: true
      });
      
      dataChannelRef.current = dataChannel;
      
      dataChannel.onopen = () => {
        console.log('Data channel opened');
      };
      
      dataChannel.onclose = () => {
        console.log('Data channel closed');
      };
      
      dataChannel.onerror = (error) => {
        console.error('Data channel error:', error);
      };
      
      // Handle ICE candidates
      peerConnection.onicecandidate = (event) => {
        if (event.candidate && signalingSocketRef.current) {
          signalingSocketRef.current.send(JSON.stringify({
            type: 'ice-candidate',
            candidate: event.candidate,
            sessionId: playResponse.sessionId
          }));
        }
      };
      
      // Setup signaling WebSocket (mock implementation)
      const signalingSocket = new WebSocket(playResponse.signaling);
      signalingSocketRef.current = signalingSocket;
      
      signalingSocket.onopen = () => {
        console.log('Signaling connected');
        // In a real implementation, you would authenticate and join the session
        signalingSocket.send(JSON.stringify({
          type: 'join-session',
          sessionId: playResponse.sessionId
        }));
      };
      
      signalingSocket.onmessage = async (event) => {
        try {
          const message = JSON.parse(event.data);
          
          switch (message.type) {
            case 'offer':
              await peerConnection.setRemoteDescription(message.offer);
              const answer = await peerConnection.createAnswer();
              await peerConnection.setLocalDescription(answer);
              signalingSocket.send(JSON.stringify({
                type: 'answer',
                answer: answer,
                sessionId: playResponse.sessionId
              }));
              break;
              
            case 'ice-candidate':
              if (message.candidate) {
                await peerConnection.addIceCandidate(message.candidate);
              }
              break;
              
            case 'session-ready':
              setConnected(true);
              setLoading(false);
              break;
              
            default:
              console.log('Unknown signaling message:', message);
          }
        } catch (error) {
          console.error('Error handling signaling message:', error);
        }
      };
      
      signalingSocket.onerror = (error) => {
        console.error('Signaling error:', error);
        setError('Signaling connection failed');
        setLoading(false);
      };

      signalingSocket.onclose = () => {
        console.log('Signaling disconnected');
        setConnected(false);
      };
      
    } catch (error) {
      console.error('Error setting up WebRTC:', error);
      setError('Failed to setup WebRTC connection');
      setLoading(false);
    }
  }, [state.videoRef]);
  
  // Launch game streaming session
  const launchGame = useCallback(async () => {
    if (!gameId) {
      setError('No gameId provided');
      return;
    }

    setLoading(true);
    setError(null);
    
    try {
      // Call the play API
      const response = await fetch('/api/play', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ gameId })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const playResponse: PlayResponse = await response.json();
      
      // Verify we got streaming mode
      if (playResponse.mode !== 'stream') {
        throw new Error('Expected streaming mode but got: ' + playResponse.mode);
      }
      
      setSessionId(playResponse.sessionId);
      
      // Setup WebRTC connection
      await setupWebRTC(playResponse);
      
    } catch (error) {
      console.error('Error launching game:', error);
      setError(error instanceof Error ? error.message : 'Failed to launch game');
      setLoading(false);
    }
  }, [gameId, setupWebRTC]);
  
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Close WebRTC connections
      if (dataChannelRef.current) {
        dataChannelRef.current.close();
      }
      if (peerConnectionRef.current) {
        peerConnectionRef.current.close();
      }
      if (signalingSocketRef.current) {
        signalingSocketRef.current.close();
      }
    };
  }, []);
  
  return {
    loading,
    error,
    connected,
    sessionId,
    videoRef,
    launchGame,
    sendInputEvent,
    isReady: connected && !loading && !error
  };
}
