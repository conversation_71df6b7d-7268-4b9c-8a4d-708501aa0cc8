#!/bin/bash

# Unity Cloud Deployment Script for AWS EC2
# This script can be run manually on your EC2 instance for deployment

set -e  # Exit on any error

echo "🚀 Unity Cloud Deployment Script Starting..."
echo "================================================"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found! Make sure you're in the unity-cloud directory."
    exit 1
fi

# Check if git is available
if ! command -v git &> /dev/null; then
    print_error "Git is not installed or not in PATH"
    exit 1
fi

# Check if node is available
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed or not in PATH"
    exit 1
fi

# Check if npm is available
if ! command -v npm &> /dev/null; then
    print_error "npm is not installed or not in PATH"
    exit 1
fi

print_status "Environment checks passed"

# Pull latest code
echo -e "\n📥 Pulling latest code from repository..."
if git pull origin main; then
    print_status "Code updated successfully"
else
    print_error "Failed to pull latest code"
    exit 1
fi

# Install/update dependencies
echo -e "\n📦 Installing dependencies..."
if npm ci --production; then
    print_status "Dependencies installed successfully"
else
    print_error "Failed to install dependencies"
    exit 1
fi

# Build the application
echo -e "\n🔨 Building Next.js application..."
if npm run build; then
    print_status "Application built successfully"
else
    print_error "Build failed"
    exit 1
fi

# Check if PM2 is installed
if ! command -v pm2 &> /dev/null; then
    print_warning "PM2 is not installed. Installing PM2 globally..."
    if npm install -g pm2; then
        print_status "PM2 installed successfully"
    else
        print_error "Failed to install PM2"
        exit 1
    fi
fi

# Restart the application with PM2
echo -e "\n🔄 Managing application with PM2..."

# Check if the app is already running
if pm2 list | grep -q "unity-cloud"; then
    print_status "Restarting existing application..."
    if pm2 restart unity-cloud; then
        print_status "Application restarted successfully"
    else
        print_error "Failed to restart application"
        exit 1
    fi
else
    print_status "Starting new application instance..."
    if pm2 start npm --name "unity-cloud" -- start; then
        print_status "Application started successfully"
    else
        print_error "Failed to start application"
        exit 1
    fi
fi

# Save PM2 configuration
print_status "Saving PM2 configuration..."
pm2 save

# Show application status
echo -e "\n📊 Application Status:"
pm2 show unity-cloud

echo -e "\n🎉 Deployment completed successfully!"
echo "================================================"
print_status "Your Unity Cloud application is now running!"

# Show useful commands
echo -e "\n📋 Useful PM2 Commands:"
echo "  pm2 status           - View all applications"
echo "  pm2 logs unity-cloud - View application logs"
echo "  pm2 restart unity-cloud - Restart the application"
echo "  pm2 stop unity-cloud - Stop the application"
echo "  pm2 delete unity-cloud - Delete the application from PM2"
