<!DOCTYPE html>
<html lang="en-us">

<head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>%UNITY_WEB_NAME%</title>
    <link rel="shortcut icon" href="data:image/x-icon;," type="image/x-icon">
    <style>
        html {
            box-sizing: border-box;
        }

        *,
        *:before,
        *:after {
            box-sizing: inherit;
        }

        body {
            margin: 0;
            padding: 0;
            background: #000;
            color: #fff;
            overflow: hidden;
        }

        #unity-container {
            width: 100%;
            height: 100vh;
            position: relative;
            background: #000;
        }

        #unity-canvas {
            width: 100%;
            height: 100%;
            background: #000;
            display: none;
            /* Initially hidden until loaded */
        }

        #loading-screen {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: #000;
            transition: opacity 0.3s ease-in-out;
        }

        #unity-loading-bar {
            width: 80%;
            max-width: 400px;
            margin: 10px;
        }

        #unity-progress-bar-empty {
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            overflow: hidden;
        }

        #unity-progress-bar-full {
            width: 0%;
            height: 100%;
            background: #fff;
            border-radius: 10px;
            transition: width 0.1s ease-in-out;
        }

        #unity-loading-text {
            font-family: Arial, sans-serif;
            font-size: 14px;
            color: #fff;
            margin-top: 10px;
        }

        #unity-warning {
            position: absolute;
            left: 50%;
            top: 5%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            padding: 10px 20px;
            border-radius: 5px;
            color: #fff;
            font-family: Arial, sans-serif;
            font-size: 14px;
            display: none;
        }

        .fade {
            transition: opacity 0.3s ease-in-out;
            opacity: 0;
            pointer-events: none;
        }
    </style>
</head>

<body>
    <div id="unity-container">
        <canvas id="unity-canvas"></canvas>
        <div id="loading-screen">
            <div id="unity-loading-bar">
                <div id="unity-progress-bar-empty">
                    <div id="unity-progress-bar-full"></div>
                </div>
                <div id="unity-loading-text">Loading...</div>
            </div>
        </div>
        <div id="unity-warning"></div>
    </div>
    <script>
        var buildUrl = "Build";
        var config = {
            dataUrl: buildUrl + "/%UNITY_DATA_URL%",
            frameworkUrl: buildUrl + "/%UNITY_FRAMEWORK_URL%",
            codeUrl: buildUrl + "/%UNITY_CODE_URL%",
            streamingAssetsUrl: "StreamingAssets",
            companyName: "%UNITY_COMPANY_NAME%",
            productName: "%UNITY_PRODUCT_NAME%",
            productVersion: "%UNITY_PRODUCT_VERSION%"
        };

        var container = document.querySelector("#unity-container");
        var canvas = document.querySelector("#unity-canvas");
        var loadingScreen = document.querySelector("#loading-screen");
        var progressBarFull = document.querySelector("#unity-progress-bar-full");
        var loadingText = document.querySelector("#unity-loading-text");
        var warningBanner = document.querySelector("#unity-warning");

        // Show canvas and set size
        canvas.style.display = "block";
        canvas.style.width = "100%";
        canvas.style.height = "100%";

        // Mobile warning
        if (/iPhone|iPad|iPod|Android/i.test(navigator.userAgent)) {
            warningBanner.style.display = "block";
            warningBanner.innerHTML = "Please note that Unity WebGL is not fully supported on mobile devices.";
        }

        // Wait for Unity loader to be ready
        function createUnityLoader() {
            if (typeof createUnityInstance === "undefined") {
                setTimeout(createUnityLoader, 100);
                return;
            }

            // Create Unity instance
            createUnityInstance(canvas, config, (progress) => {
                progressBarFull.style.width = (100 * progress) + "%";
                loadingText.innerHTML = "Loading... " + Math.round(progress * 100) + "%";
            }).then((unityInstance) => {
                loadingScreen.classList.add("fade");
                setTimeout(() => {
                    loadingScreen.remove();
                }, 300);
            }).catch((message) => {
                console.error("Unity error:", message);
                warningBanner.style.display = "block";
                warningBanner.innerHTML = message;
            });
        }

        // Start the Unity loader
        createUnityLoader();
    </script>
</body>

</html>