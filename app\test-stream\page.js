'use client';

import { useState } from 'react';
import GamePlayer from '../components/GamePlayer';

export default function TestStreamPage() {
  const [gameId, setGameId] = useState('test-game-123');
  const [showPlayer, setShowPlayer] = useState(false);

  return (
    <div style={{ padding: '20px', minHeight: '100vh', background: '#1a1a1a', color: '#fff' }}>
      <h1>WebRTC Game Streaming Test</h1>
      
      {!showPlayer ? (
        <div style={{ maxWidth: '600px' }}>
          <p>This page demonstrates the WebRTC streaming functionality.</p>
          
          <div style={{ margin: '20px 0' }}>
            <label style={{ display: 'block', marginBottom: '8px' }}>
              Game ID:
            </label>
            <input
              type="text"
              value={gameId}
              onChange={(e) => setGameId(e.target.value)}
              style={{
                padding: '8px',
                width: '300px',
                background: '#333',
                color: '#fff',
                border: '1px solid #555',
                borderRadius: '4px'
              }}
              placeholder="Enter game ID"
            />
          </div>
          
          <button
            onClick={() => setShowPlayer(true)}
            disabled={!gameId.trim()}
            style={{
              padding: '12px 24px',
              background: gameId.trim() ? '#007bff' : '#666',
              color: '#fff',
              border: 'none',
              borderRadius: '4px',
              cursor: gameId.trim() ? 'pointer' : 'not-allowed',
              fontSize: '16px'
            }}
          >
            Start Streaming Session
          </button>
          
          <div style={{ marginTop: '30px', padding: '15px', background: '#333', borderRadius: '4px' }}>
            <h3>How it works:</h3>
            <ol style={{ paddingLeft: '20px' }}>
              <li>Click "Start Streaming Session" to call <code>/api/play</code></li>
              <li>API allocates a mock GPU session and returns WebRTC connection info</li>
              <li>GamePlayer component sets up WebRTC peer connection</li>
              <li>Video stream would appear (currently mocked, so connection will fail)</li>
              <li>Input capture forwards keyboard/mouse/gamepad to server via data channel</li>
            </ol>
          </div>
          
          <div style={{ marginTop: '20px', padding: '15px', background: '#2d4a2d', borderRadius: '4px' }}>
            <h3>Production Integration:</h3>
            <ul style={{ paddingLeft: '20px' }}>
              <li>Replace <code>allocateGpuSession()</code> with real AWS EC2 G4/G5 allocation</li>
              <li>Deploy real WebSocket signaling server</li>
              <li>Use Unity Render Streaming in GPU containers</li>
              <li>Handle session cleanup and scaling</li>
            </ul>
          </div>
        </div>
      ) : (
        <div style={{ position: 'relative' }}>
          <button
            onClick={() => setShowPlayer(false)}
            style={{
              position: 'absolute',
              top: '10px',
              left: '10px',
              zIndex: 1000,
              padding: '8px 16px',
              background: 'rgba(255, 255, 255, 0.2)',
              color: '#fff',
              border: '1px solid rgba(255, 255, 255, 0.3)',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            ← Back to Test Page
          </button>
          
          <GamePlayer gameId={gameId} />
        </div>
      )}
    </div>
  );
}
