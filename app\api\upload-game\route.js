import { NextResponse } from 'next/server';
import { s3Client, BUCKET_NAME, CLOUDFRONT_DOMAIN } from '@/lib/config';
import { PutObjectCommand } from '@aws-sdk/client-s3';
import { v4 as uuidv4 } from 'uuid';
import AdmZip from 'adm-zip';
import fs from 'fs/promises';
import path from 'path';
// Required Unity WebGL build files (using .gz compression)
const REQUIRED_FILES = ['.js.gz', '.wasm.gz', '.data.gz', 'index.html'];

async function getUnityTemplate() {
    const templatePath = path.join(process.cwd(), 'public', 'unity-template.html');
    return await fs.readFile(templatePath, 'utf8');
}

function extractUnityConfig(originalHtml) {
    // Extract all possible Unity configuration
    const config = {
        buildUrl: "./Build",  // Default to relative path
        loaderUrl: "",
        dataUrl: "",
        frameworkUrl: "",
        codeUrl: "",
        streamingAssetsUrl: "StreamingAssets",
        companyName: "",
        productName: "",
        productVersion: "",
        showBanner: false
    };

    // Regular expressions to match Unity configuration
    const regexPatterns = {
        buildUrl: /var\s+buildUrl\s*=\s*["']([^"']+)["']/,
        loaderUrl: /loaderUrl\s*=\s*["']([^"']+)["']|loaderUrl\s*=\s*buildUrl\s*\+\s*["']([^"']+)["']/,
        dataUrl: /dataUrl\s*:\s*["']([^"']+)["']|dataUrl\s*:\s*buildUrl\s*\+\s*["']([^"']+)["']/,
        frameworkUrl: /frameworkUrl\s*:\s*["']([^"']+)["']|frameworkUrl\s*:\s*buildUrl\s*\+\s*["']([^"']+)["']/,
        codeUrl: /codeUrl\s*:\s*["']([^"']+)["']|codeUrl\s*:\s*buildUrl\s*\+\s*["']([^"']+)["']/,
        streamingAssetsUrl: /streamingAssetsUrl:\s*["']([^"']+)["']/,
        companyName: /companyName:\s*["']([^"']+)["']/,
        productName: /productName:\s*["']([^"']+)["']/,
        productVersion: /productVersion:\s*["']([^"']+)["']/
    };

    // Extract each configuration value
    for (const [key, regex] of Object.entries(regexPatterns)) {
        const match = originalHtml.match(regex);
        if (match) {
            // Some URLs might be full paths or relative to buildUrl
            if (['loaderUrl', 'dataUrl', 'frameworkUrl', 'codeUrl'].includes(key)) {
                config[key] = match[2] ? match[2] : match[1];  // match[2] is the path after buildUrl if it exists
            } else {
                config[key] = match[1];
            }
        }
    }

    // Extract any memory configuration
    const memoryMatch = originalHtml.match(/memoryUrl\s*:\s*["']([^"']+)["']|memoryUrl\s*:\s*buildUrl\s*\+\s*["']([^"']+)["']/);
    if (memoryMatch) {
        config.memoryUrl = memoryMatch[2] ? memoryMatch[2] : memoryMatch[1];
    }

    // Extract script src for the Unity loader
    const loaderScriptMatch = originalHtml.match(/<script[^>]+src=["']([^"']+\.js)["'][^>]*>/);
    if (loaderScriptMatch) {
        config.loaderScript = loaderScriptMatch[1];
    }

    return config;
}

function customizeUnityTemplate(template, originalHtml) {
    const config = extractUnityConfig(originalHtml);

    // Extract the name of the loader file from the full path
    const loaderFileName = config.loaderUrl.split('/').pop();

    // Replace placeholders in template
    let customized = template
        .replace(/%UNITY_WEB_NAME%/g, config.productName || 'Unity WebGL Game')
        .replace(/%UNITY_LOADER_URL%/g, loaderFileName)
        .replace(/%UNITY_DATA_URL%/g, config.dataUrl.replace(/^\//, ''))
        .replace(/%UNITY_FRAMEWORK_URL%/g, config.frameworkUrl.replace(/^\//, ''))
        .replace(/%UNITY_CODE_URL%/g, config.codeUrl.replace(/^\//, ''))
        .replace(/%UNITY_COMPANY_NAME%/g, config.companyName || '')
        .replace(/%UNITY_PRODUCT_NAME%/g, config.productName || '')
        .replace(/%UNITY_PRODUCT_VERSION%/g, config.productVersion || '1.0')
        // Add script loading for Unity loader
        .replace('</head>', `    <script src="Build/${loaderFileName}"></script>\n  </head>`);

    // Add memory and symbols URLs if they exist
    if (config.memoryUrl) {
        customized = customized.replace(
            'streamingAssetsUrl: "StreamingAssets",',
            `memoryUrl: buildUrl + "/${config.memoryUrl.replace(/^\//, '')}",\n        streamingAssetsUrl: "StreamingAssets",`
        );
    }

    return customized;
}

function isValidUnityBuild(entries) {
    const fileNames = entries.map(e => e.entryName);
    return REQUIRED_FILES.every(type => fileNames.some(name => name.endsWith(type)));
}

function getMimeTypeAndEncoding(filename) {
    if (filename.endsWith('.js.gz')) return ['application/javascript', 'gzip'];
    if (filename.endsWith('.wasm.gz')) return ['application/wasm', 'gzip'];
    if (filename.endsWith('.data.gz')) return ['application/octet-stream', 'gzip'];
    if (filename.endsWith('.js')) return ['application/javascript'];
    if (filename.endsWith('.html')) return ['text/html'];
    return ['application/octet-stream'];
}

export async function POST(request) {
    const formData = await request.formData();
    const file = formData.get('file');

    if (!file) {
        return NextResponse.json({ error: 'Missing file' }, { status: 400 });
    }

    try {
        const uniqueId = uuidv4();
        const buffer = await file.arrayBuffer();
        const zip = new AdmZip(Buffer.from(buffer));
        const entries = zip.getEntries();

        if (!isValidUnityBuild(entries)) {
            return NextResponse.json({
                error: 'Invalid Unity WebGL build structure',
                message: 'Missing required files. Required files are: ' + REQUIRED_FILES.join(', ')
            }, { status: 400 });
        }

        // Load our custom template
        const template = await getUnityTemplate();

        // First find and process index.html to get configuration
        const indexEntry = entries.find(entry => entry.entryName.endsWith('index.html'));
        if (!indexEntry) {
            throw new Error('index.html not found in the build');
        }

        const originalHtml = indexEntry.getData().toString('utf8');
        const customizedHtml = customizeUnityTemplate(template, originalHtml);

        // Process and upload files
        const uploadPromises = entries.map(async entry => {
            if (entry.isDirectory) {
                return Promise.resolve();
            }

            let fileBuffer = entry.getData();
            let entryPath = entry.entryName;            
            // Skip TemplateData folder and handle other paths
            if (entryPath.includes('TemplateData/')) {
                return Promise.resolve();
            }

            // Remove any initial folder if present
            const normalizedPath = entryPath.includes('/') ?
                entryPath.substring(entryPath.indexOf('/') + 1) :
                entryPath;

            if (!normalizedPath) {
                return Promise.resolve();
            }

            // Use our customized index.html
            if (normalizedPath === 'index.html') {
                fileBuffer = Buffer.from(customizedHtml, 'utf8');
            }

            // Handle file paths to avoid nested Build directories
            const finalPath = normalizedPath === 'index.html' ? normalizedPath : 
                normalizedPath.startsWith('Build/') ? normalizedPath :
                `Build/${normalizedPath}`;

            const [contentType, contentEncoding] = getMimeTypeAndEncoding(normalizedPath);

            const uploadParams = {
                Bucket: BUCKET_NAME,
                Key: `${uniqueId}/${finalPath}`,
                Body: fileBuffer,
                ContentType: contentType,
                ...(contentEncoding && { ContentEncoding: contentEncoding }),
                CacheControl: 'public, max-age=31536000'
            };

            return s3Client.send(new PutObjectCommand(uploadParams));
        });

        await Promise.all(uploadPromises.filter(Boolean));

        const gameUrl = `https://${CLOUDFRONT_DOMAIN}/${uniqueId}/index.html`;

        return NextResponse.json({
            message: 'Game uploaded successfully',
            id: uniqueId,
            gameUrl
        });
    } catch (error) {
        console.error('Error processing Unity build:', error);
        return NextResponse.json({
            error: 'Error processing Unity build',
            details: error.message
        }, { status: 500 });
    }
}
