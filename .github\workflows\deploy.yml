# GitHub Actions CI/CD Pipeline for Unity Cloud (Next.js)
# This workflow will build and deploy your Next.js app to AWS EC2

name: Deploy Unity Cloud to AWS EC2

# When should this workflow run?
on:
  push:
    branches: [ main, master ]  # Triggers when you push to main or master branch
  pull_request:
    branches: [ main, master ]  # Triggers when someone creates a pull request

# Define the jobs that will run
jobs:
  # Job 1: Build and Test
  build-and-test:
    name: Build and Test Application
    runs-on: ubuntu-latest  # Use Ubuntu server for building
    
    steps:
      # Step 1: Get the code from your repository
      - name: Checkout code
        uses: actions/checkout@v4
        
      # Step 2: Setup Node.js (required for Next.js)
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'  # Use Node.js version 18
          cache: 'npm'        # Cache npm dependencies for faster builds
          
      # Step 3: Install all dependencies
      - name: Install dependencies
        run: npm ci  # npm ci is faster and more reliable for CI/CD
        
      # Step 4: Run linting to check code quality
      - name: Run ESLint
        run: npm run lint
        
      # Step 5: Build the Next.js application
      - name: Build application
        run: npm run build
        
      # Step 6: Upload build artifacts for deployment job
      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-files
          path: |
            .next/
            public/
            package.json
            package-lock.json
          retention-days: 1

  # Job 2: Deploy to EC2 (only runs if build-and-test succeeds)
  deploy:
    name: Deploy to AWS EC2
    runs-on: ubuntu-latest
    needs: build-and-test  # This job waits for build-and-test to complete successfully
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master'  # Only deploy from main/master
    
    steps:
      # Step 1: Get the code again
      - name: Checkout code
        uses: actions/checkout@v4
        
      # Step 2: Download the build artifacts from the previous job
      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-files
          
      # Step 3: Deploy to EC2 using SSH
      - name: Deploy to EC2
        uses: appleboy/ssh-action@v1.1.0
        with:
          # These are secrets you'll need to set in GitHub
          host: ${{ secrets.EC2_HOST }}           # Your EC2 instance IP address
          username: ${{ secrets.EC2_USERNAME }}   # Usually 'ec2-user' or 'ubuntu'
          key: ${{ secrets.EC2_PRIVATE_KEY }}     # Your EC2 private key (.pem file content)
          port: 22                                # SSH port (usually 22)
          
          # Commands to run on your EC2 instance
          script: |
            echo "🚀 Starting deployment process..."
            
            # Navigate to your application directory
            cd /home/<USER>/unity-cloud || {
              echo "❌ Application directory not found!"
              exit 1
            }
            
            # Pull the latest code from GitHub
            echo "📥 Pulling latest code..."
            git pull origin main
            
            # Install/update dependencies
            echo "📦 Installing dependencies..."
            npm ci --production
            
            # Build the application
            echo "🔨 Building application..."
            npm run build
            
            # Restart the application using PM2 (Process Manager)
            echo "🔄 Restarting application..."
            pm2 restart unity-cloud || pm2 start npm --name "unity-cloud" -- start
            
            # Save PM2 configuration
            pm2 save
            
            echo "✅ Deployment completed successfully!"
            
      # Step 4: Notify about deployment status
      - name: Deployment Status
        if: always()  # This runs regardless of previous step success/failure
        run: |
          if [ "${{ job.status }}" == "success" ]; then
            echo "🎉 Deployment successful! Your app is now live."
          else
            echo "❌ Deployment failed. Please check the logs."
          fi
