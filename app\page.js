'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Box, Grid, Typography, Button } from '@mui/material';

export default function Home() {
  const [games, setGames] = useState([]);
  const [error, setError] = useState('');
  const [fetchingGames, setFetchingGames] = useState(true);

  useEffect(() => {
    const fetchGames = async () => {
      try {
        setFetchingGames(true);
        const response = await fetch('/api/games');
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        setGames(data.games || []);
      } catch (err) {
        setError('Failed to load game list: ' + err.message);
      } finally {
        setFetchingGames(false);
      }
    };

    fetchGames();
  }, []);

  if (fetchingGames) {
    return (
      <div className="container">
        <h1>Unity WebGL Games</h1>
        <div className="loading">Loading available games...</div>
      </div>
    );
  }

  if (games.length === 0 && !error) {
    return (
      <div className="container">
        <h1>Unity WebGL Games</h1>
        <div className="no-games">
          No games available.
          <Link href="/upload" className="upload-link">
            Upload your first game
          </Link>
        </div>
      </div>
    );
  }


  function getGradientByIndex(index) {
    const hue1 = (index * 47) % 360;
    const hue2 = (hue1 + 60) % 360;
    return `linear-gradient(135deg, hsl(${hue1}, 70%, 60%), hsl(${hue2}, 70%, 60%))`;
  }

  return (
    <Box sx={{ p: 4 }}>
      <Typography variant="h4" gutterBottom>
        Uploaded Games
      </Typography>

      {error && (
        <Typography color="error" sx={{ mb: 2 }}>
          {error}
        </Typography>
      )}

      <Grid container display={'grid'} spacing={3} gridTemplateColumns={{ xs: 'repeat(1, 1fr)', sm: 'repeat(2, 1fr)', md: 'repeat(3, 1fr)' }}>
        {games.map((game, index) => {
          const gradient = getGradientByIndex(index);

          return (
            <Grid item key={game.id}>
              <Box
                sx={{
                  background: gradient,
                  color: 'white',
                  borderRadius: 3,
                  p: 3,
                  // height: '200px',
                  display: 'flex',
                  // flexDirection: 'column',
                  justifyContent: 'space-between',
                  boxShadow: 3,
                  transition: 'transform 0.3s',
                  '&:hover': {
                    transform: 'scale(1.05)',
                  },
                }}
              >
                <Box display={'flex'} flexDirection={'column'} justifyContent={'space-between'}>
                  <Typography variant="h6" margin={0}>
                    Game {index + 1}
                  </Typography>
                  <Typography variant="p" gutterBottom fontSize={8}>
                    {game.id}
                  </Typography>
                </Box>
                <Link href={`/play/${game.id}`} passHref>
                  <Button variant="contained" sx={{ backgroundColor: '#fff', color: '#000' }}>
                    Play
                  </Button>
                </Link>
              </Box>
            </Grid>
          );
        })}
      </Grid>
    </Box>
  );
}
