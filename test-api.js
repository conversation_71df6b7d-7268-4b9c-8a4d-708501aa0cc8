// Simple test script to verify the /api/play endpoint
// Run with: node test-api.js (after starting the dev server)

async function testPlayAPI() {
  try {
    console.log('Testing /api/play endpoint...');
    
    const response = await fetch('http://localhost:3000/api/play', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ gameId: 'test-game-123' })
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    console.log('API Response:', JSON.stringify(data, null, 2));
    
    // Verify response structure
    if (data.mode === 'stream' && data.sessionId && data.signaling && data.iceServers) {
      console.log('✅ API test passed! Response has correct structure.');
    } else {
      console.log('❌ API test failed! Response structure is incorrect.');
    }
    
  } catch (error) {
    console.error('❌ API test failed:', error.message);
  }
}

// Only run if this file is executed directly
if (require.main === module) {
  testPlayAPI();
}
