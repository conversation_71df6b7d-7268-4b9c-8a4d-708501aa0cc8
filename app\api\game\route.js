import { NextResponse } from 'next/server';
import { ListObjectsV2Command } from '@aws-sdk/client-s3';
import { s3Client, CLOUDFRONT_DOMAIN } from '@/lib/config';

export async function GET(request) {
    const searchParams = request.nextUrl.searchParams;
    const bucket = searchParams.get('bucket');
    const gameId = searchParams.get('gameId');

    if (!bucket || !gameId) {
        return NextResponse.json({
            error: 'Missing required parameters'
        }, { status: 400 });
    }

    try {
        // Look for index.html
        const indexPath = `${gameId}/index.html`;
        const listCommand = new ListObjectsV2Command({
            Bucket: bucket,
            Prefix: indexPath
        });

        const response = await s3Client.send(listCommand);

        if (!response.Contents || response.Contents.length === 0) {
            throw new Error(`Game ${gameId} not found`);
        }

        // Return the direct URL to index.html
        const url = `https://${CLOUDFRONT_DOMAIN}/${gameId}/index.html`;
        
        return NextResponse.json({ url });

    } catch (error) {
        console.error('Error:', error);
        return NextResponse.json({
            error: error.message
        }, { status: 500 });
    }
}
