import { NextResponse } from 'next/server';
import { ListObjectsV2Command, DeleteObjectsCommand } from '@aws-sdk/client-s3';
import { s3Client, BUCKET_NAME, BUCKET_SIZE_LIMIT_MB } from '@/lib/config';

async function getBucketSize() {
    try {
        let totalSize = 0;
        let continuationToken = undefined;

        do {
            const command = new ListObjectsV2Command({
                Bucket: BUCKET_NAME,
                ContinuationToken: continuationToken
            });

            const response = await s3Client.send(command);

            if (response.Contents) {
                totalSize += response.Contents.reduce((acc, obj) => acc + obj.Size, 0);
            }

            continuationToken = response.NextContinuationToken;
        } while (continuationToken);

        return totalSize;
    } catch (error) {
        console.error('Error getting bucket size:', error);
        throw error;
    }
}

async function clearBucket() {
    try {
        let continuationToken = undefined;

        do {
            const listCommand = new ListObjectsV2Command({
                Bucket: BUCKET_NAME,
                ContinuationToken: continuationToken
            });

            const listResponse = await s3Client.send(listCommand);

            if (listResponse.Contents && listResponse.Contents.length > 0) {
                const deleteCommand = new DeleteObjectsCommand({
                    Bucket: BUCKET_NAME,
                    Delete: {
                        Objects: listResponse.Contents.map(obj => ({ Key: obj.Key })),
                        Quiet: true
                    }
                });

                await s3Client.send(deleteCommand);
            }

            continuationToken = listResponse.NextContinuationToken;
        } while (continuationToken);

        return true;
    } catch (error) {
        console.error('Error clearing bucket:', error);
        throw error;
    }
}

async function deleteGames(gameIds) {
    try {
        for (const gameId of gameIds) {
            const listCommand = new ListObjectsV2Command({
                Bucket: BUCKET_NAME,
                Prefix: `${gameId}/`
            });

            const listResponse = await s3Client.send(listCommand);

            if (listResponse.Contents && listResponse.Contents.length > 0) {
                const deleteCommand = new DeleteObjectsCommand({
                    Bucket: BUCKET_NAME,
                    Delete: {
                        Objects: listResponse.Contents.map(obj => ({ Key: obj.Key })),
                        Quiet: true
                    }
                });

                await s3Client.send(deleteCommand);
            }
        }
        return true;
    } catch (error) {
        console.error('Error deleting games:', error);
        throw error;
    }
}

export async function GET() {
    try {
        const totalSize = await getBucketSize();

        return NextResponse.json({
            size: totalSize,
            sizeInMB: Math.round(totalSize / (1024 * 1024) * 100) / 100,
            sizeLimit: BUCKET_SIZE_LIMIT_MB,
            isFull: totalSize > BUCKET_SIZE_LIMIT_MB * 1024 * 1024
        });
    } catch (error) {
        return NextResponse.json({
            error: 'Failed to get bucket info',
            details: error.message
        }, { status: 500 });
    }
}

export async function DELETE(request) {
    try {
        const { gameIds } = await request.json();

        if (gameIds && Array.isArray(gameIds)) {
            await deleteGames(gameIds);
            return NextResponse.json({ message: 'Selected games deleted successfully' });
        } else {
            // If no gameIds provided, clear entire bucket (backward compatibility)
            await clearBucket();
            return NextResponse.json({ message: 'Bucket cleared successfully' });
        }
    } catch (error) {
        return NextResponse.json({
            error: 'Failed to delete games',
            details: error.message
        }, { status: 500 });
    }
}